"""Configuration settings for the 4Runner scraper application."""

import os
from pathlib import Path

# Base directory
BASE_DIR = Path(__file__).parent

# Database configuration
DATABASE_URL = os.getenv('DATABASE_URL', f'sqlite:///{BASE_DIR}/4runner_data.db')

# Search parameters
SEARCH_CONFIG = {
    'make': 'Toyota',
    'model': '4Runner',
    'engine': 'V6',
    'location': 'San Jose, CA',
    'radius_miles': 50,
    'min_year': 2000,  # Reasonable minimum year for V6 4Runners
    'max_year': 2024
}

# Scraping configuration
SCRAPING_CONFIG = {
    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'request_delay': 2,  # Seconds between requests
    'timeout': 30,
    'max_retries': 3
}

# Supported platforms
PLATFORMS = {
    'craigslist': {
        'enabled': True,
        'base_url': 'https://sfbay.craigslist.org/search/cta',
        'search_params': {
            'query': 'toyota 4runner v6',
            'search_distance': 50,
            'postal': '95110'  # San Jose ZIP
        }
    },
    'autotrader': {
        'enabled': True,
        'base_url': 'https://www.autotrader.com/cars-for-sale',
        'search_params': {
            'makeCodeList': 'TOYOTA',
            'modelCodeList': '4RUN',
            'zip': '95110',
            'searchRadius': 50
        }
    },
    'cars_com': {
        'enabled': True,
        'base_url': 'https://www.cars.com/shopping/results/',
        'search_params': {
            'make_model_list[]': 'toyota-4runner',
            'zip': '95110',
            'maximum_distance': 50
        }
    }
}

# Analytics configuration
ANALYTICS_CONFIG = {
    'mileage_buckets': [
        (0, 50000, '0-50k'),
        (50001, 100000, '50k-100k'),
        (100001, 150000, '100k-150k'),
        (150001, 200000, '150k-200k'),
        (200001, float('inf'), '200k+')
    ],
    'deal_threshold_percentage': 0.85  # Consider deals below 85% of market average
}

# Dashboard configuration
DASHBOARD_CONFIG = {
    'host': '127.0.0.1',
    'port': 8501,
    'theme': 'light'
}

# Scheduling configuration
SCHEDULER_CONFIG = {
    'scrape_hour': 8,  # Run daily scraping at 8 AM
    'scrape_minute': 0,
    'timezone': 'America/Los_Angeles'
}
