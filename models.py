"""Database models for the 4Runner scraper application."""

from datetime import datetime
from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Boolean, Text, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from config import DATABASE_URL

Base = declarative_base()

class CarListing(Base):
    """Model for storing car listing data."""
    
    __tablename__ = 'car_listings'
    
    id = Column(Integer, primary_key=True)
    
    # Car details
    make = Column(String(50), nullable=False, default='Toyota')
    model = Column(String(50), nullable=False, default='4Runner')
    year = Column(Integer, nullable=False)
    price = Column(Float, nullable=False)
    mileage = Column(Integer)
    
    # Listing details
    title = Column(String(500))
    description = Column(Text)
    location = Column(String(200))
    seller_type = Column(String(20))  # 'dealer' or 'private'
    vin = Column(String(17))
    listing_url = Column(String(1000), nullable=False)
    platform = Column(String(50), nullable=False)
    
    # Images and contact
    image_urls = Column(Text)  # JSON string of image URLs
    contact_info = Column(String(500))
    
    # Metadata
    scraped_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    last_seen = Column(DateTime, default=datetime.utcnow)
    
    # Create indexes for common queries
    __table_args__ = (
        Index('idx_year_price', 'year', 'price'),
        Index('idx_mileage_price', 'mileage', 'price'),
        Index('idx_platform_scraped', 'platform', 'scraped_at'),
        Index('idx_vin', 'vin'),
        Index('idx_active_listings', 'is_active', 'last_seen'),
    )
    
    def __repr__(self):
        return f"<CarListing({self.year} {self.make} {self.model}, ${self.price:,.0f}, {self.mileage:,} miles)>"
    
    def to_dict(self):
        """Convert listing to dictionary."""
        return {
            'id': self.id,
            'make': self.make,
            'model': self.model,
            'year': self.year,
            'price': self.price,
            'mileage': self.mileage,
            'title': self.title,
            'location': self.location,
            'seller_type': self.seller_type,
            'vin': self.vin,
            'listing_url': self.listing_url,
            'platform': self.platform,
            'scraped_at': self.scraped_at,
            'is_active': self.is_active,
            'last_seen': self.last_seen
        }

class PriceHistory(Base):
    """Model for tracking price changes over time."""
    
    __tablename__ = 'price_history'
    
    id = Column(Integer, primary_key=True)
    listing_id = Column(Integer, nullable=False)  # Reference to CarListing.id
    vin = Column(String(17))
    listing_url = Column(String(1000), nullable=False)
    
    # Price tracking
    old_price = Column(Float)
    new_price = Column(Float, nullable=False)
    price_change = Column(Float)  # new_price - old_price
    change_percentage = Column(Float)  # (new_price - old_price) / old_price * 100
    
    # Metadata
    changed_at = Column(DateTime, default=datetime.utcnow)
    platform = Column(String(50), nullable=False)
    
    __table_args__ = (
        Index('idx_vin_changed', 'vin', 'changed_at'),
        Index('idx_listing_changed', 'listing_id', 'changed_at'),
    )
    
    def __repr__(self):
        return f"<PriceHistory(${self.old_price} -> ${self.new_price}, {self.change_percentage:.1f}%)>"

class ScrapingLog(Base):
    """Model for tracking scraping sessions."""
    
    __tablename__ = 'scraping_logs'
    
    id = Column(Integer, primary_key=True)
    platform = Column(String(50), nullable=False)
    started_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime)
    
    # Results
    listings_found = Column(Integer, default=0)
    new_listings = Column(Integer, default=0)
    updated_listings = Column(Integer, default=0)
    errors_count = Column(Integer, default=0)
    
    # Status
    status = Column(String(20), default='running')  # 'running', 'completed', 'failed'
    error_message = Column(Text)
    
    def __repr__(self):
        return f"<ScrapingLog({self.platform}, {self.status}, {self.listings_found} found)>"

# Database setup
engine = create_engine(DATABASE_URL, echo=False)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def create_tables():
    """Create all database tables."""
    Base.metadata.create_all(bind=engine)

def get_db_session():
    """Get a database session."""
    return SessionLocal()

def init_database():
    """Initialize the database with tables."""
    create_tables()
    print("Database initialized successfully!")

if __name__ == "__main__":
    init_database()
