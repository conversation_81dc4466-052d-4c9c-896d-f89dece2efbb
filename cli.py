"""Command-line interface for the 4Runner scraper application."""

import click
import subprocess
import sys
from datetime import datetime
from pathlib import Path

from models import init_database
from scrapers.scraper_manager import ScraperManager
from analytics import AnalyticsEngine

@click.group()
@click.version_option(version='1.0.0')
def cli():
    """Toyota 4Runner V6 Car Listing Scraper and Analyzer
    
    A comprehensive tool for scraping, analyzing, and tracking Toyota 4Runner V6 
    listings in the San Jose, CA area.
    """
    pass

@cli.command()
def init():
    """Initialize the database and create tables."""
    try:
        init_database()
        click.echo("✅ Database initialized successfully!")
    except Exception as e:
        click.echo(f"❌ Error initializing database: {e}")
        sys.exit(1)

@cli.command()
@click.option('--platform', '-p', help='Specific platform to scrape (craigslist, cars_com, etc.)')
@click.option('--parallel/--sequential', default=True, help='Run scrapers in parallel or sequentially')
def scrape(platform, parallel):
    """Run the car listing scrapers."""
    click.echo("🔍 Starting car listing scraper...")
    
    manager = ScraperManager()
    
    if platform:
        # Run single platform
        click.echo(f"Scraping {platform}...")
        result = manager.run_single_scraper(platform)
        display_scraper_result(result)
    else:
        # Run all platforms
        click.echo("Scraping all enabled platforms...")
        results = manager.run_all_scrapers(parallel=parallel)
        
        click.echo("\n📊 Scraping Results:")
        click.echo("=" * 50)
        
        total_found = 0
        total_new = 0
        total_updated = 0
        
        for result in results:
            display_scraper_result(result)
            if result['status'] == 'success':
                total_found += result.get('listings_found', 0)
                total_new += result.get('new_listings', 0)
                total_updated += result.get('updated_listings', 0)
        
        click.echo("\n📈 Summary:")
        click.echo(f"Total listings found: {total_found}")
        click.echo(f"New listings: {total_new}")
        click.echo(f"Updated listings: {total_updated}")

def display_scraper_result(result):
    """Display results from a single scraper."""
    platform = result['platform']
    status = result['status']
    
    if status == 'success':
        click.echo(f"✅ {platform}: {result['listings_found']} found, "
                  f"{result['new_listings']} new, {result['updated_listings']} updated")
    else:
        click.echo(f"❌ {platform}: {result.get('error', 'Unknown error')}")

@cli.command()
@click.option('--deals', '-d', is_flag=True, help='Show best deals only')
@click.option('--trends', '-t', is_flag=True, help='Show price trends')
@click.option('--limit', '-l', default=10, help='Limit number of results')
def analyze(deals, trends, limit):
    """Analyze car listing data and show insights."""
    click.echo("📊 Analyzing car listing data...")
    
    analytics = AnalyticsEngine()
    
    try:
        if deals:
            show_best_deals(analytics, limit)
        elif trends:
            show_price_trends(analytics, limit)
        else:
            show_summary_analysis(analytics)
    except Exception as e:
        click.echo(f"❌ Error during analysis: {e}")
    finally:
        analytics.close()

def show_summary_analysis(analytics):
    """Show summary analysis."""
    stats = analytics.get_summary_stats()
    
    if 'error' in stats:
        click.echo(f"❌ {stats['error']}")
        return
    
    click.echo("\n📈 Summary Statistics:")
    click.echo("=" * 40)
    click.echo(f"Total active listings: {stats['total_listings']}")
    click.echo(f"Average price: ${stats['price_stats']['mean']:,.0f}")
    click.echo(f"Median price: ${stats['price_stats']['median']:,.0f}")
    click.echo(f"Price range: ${stats['price_stats']['min']:,.0f} - ${stats['price_stats']['max']:,.0f}")
    click.echo(f"Year range: {stats['year_range']['min']} - {stats['year_range']['max']}")
    
    if stats['mileage_stats']['mean']:
        click.echo(f"Average mileage: {stats['mileage_stats']['mean']:,.0f} miles")
    
    click.echo("\n🏪 Platforms:")
    for platform, count in stats['platforms'].items():
        click.echo(f"  {platform}: {count} listings")
    
    click.echo("\n👥 Seller Types:")
    for seller_type, count in stats['seller_types'].items():
        click.echo(f"  {seller_type}: {count} listings")
    
    # Show price by year
    click.echo("\n📅 Average Price by Year:")
    year_stats = analytics.get_price_by_year()
    for year, data in sorted(year_stats.items()):
        click.echo(f"  {year}: ${data['mean']:,.0f} avg (${data['median']:,.0f} median, {data['count']} listings)")

def show_best_deals(analytics, limit):
    """Show best deals."""
    deals = analytics.find_best_deals(limit)
    
    if not deals:
        click.echo("❌ No deals found")
        return
    
    click.echo(f"\n💰 Top {len(deals)} Best Deals:")
    click.echo("=" * 80)
    
    for i, deal in enumerate(deals, 1):
        savings_pct = deal['deal_score'] * 100
        click.echo(f"{i}. {deal['year']} Toyota 4Runner - ${deal['price']:,.0f}")
        click.echo(f"   💾 {deal['mileage']:,} miles | 📍 {deal['location']}")
        click.echo(f"   💰 Save ${deal['savings']:,.0f} ({savings_pct:.1f}% below market avg)")
        click.echo(f"   🔗 {deal['listing_url']}")
        click.echo()

def show_price_trends(analytics, days):
    """Show price trends."""
    trends = analytics.get_price_trends(days)
    
    if not trends:
        click.echo(f"❌ No price changes found in the last {days} days")
        return
    
    click.echo(f"\n📈 Price Changes (Last {days} days):")
    click.echo("=" * 60)
    
    for trend in trends[:10]:  # Show top 10
        change_symbol = "📉" if trend['price_change'] < 0 else "📈"
        click.echo(f"{change_symbol} ${trend['old_price']:,.0f} → ${trend['new_price']:,.0f} "
                  f"({trend['change_percentage']:+.1f}%)")
        click.echo(f"   🔗 {trend['listing_url']}")
        click.echo()

@cli.command()
@click.option('--format', '-f', type=click.Choice(['csv', 'excel']), default='csv', 
              help='Export format')
@click.option('--filename', '-o', help='Output filename')
def export(format, filename):
    """Export data to CSV or Excel file."""
    click.echo(f"📤 Exporting data to {format.upper()}...")
    
    analytics = AnalyticsEngine()
    
    try:
        if format == 'csv':
            output_file = analytics.export_to_csv(filename)
        else:
            output_file = analytics.export_to_excel(filename)
        
        click.echo(f"✅ Data exported to: {output_file}")
        
    except Exception as e:
        click.echo(f"❌ Export failed: {e}")
    finally:
        analytics.close()

@cli.command()
@click.option('--port', '-p', default=8080, help='Port to run dashboard on')
def dashboard(port):
    """Launch the Streamlit dashboard."""
    dashboard_file = Path(__file__).parent / "dashboard.py"
    
    if not dashboard_file.exists():
        click.echo("❌ Dashboard file not found. Make sure dashboard.py exists.")
        sys.exit(1)
    
    click.echo(f"🚀 Starting dashboard on port {port}...")
    click.echo(f"🌐 Open your browser to: http://localhost:{port}")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            str(dashboard_file), "--server.port", str(port)
        ])
    except KeyboardInterrupt:
        click.echo("\n👋 Dashboard stopped.")
    except Exception as e:
        click.echo(f"❌ Error starting dashboard: {e}")

@cli.command()
def status():
    """Show application status and statistics."""
    click.echo("📊 Application Status:")
    click.echo("=" * 30)
    
    # Check database
    try:
        analytics = AnalyticsEngine()
        stats = analytics.get_summary_stats()
        analytics.close()
        
        if 'error' not in stats:
            click.echo(f"✅ Database: Connected ({stats['total_listings']} active listings)")
        else:
            click.echo("❌ Database: No data found")
    except Exception as e:
        click.echo(f"❌ Database: Connection failed ({e})")
    
    # Check scrapers
    manager = ScraperManager()
    scraper_status = manager.get_scraper_status()
    click.echo(f"🔍 Scrapers: {scraper_status['total_scrapers']} enabled")
    for scraper in scraper_status['enabled_scrapers']:
        click.echo(f"  ✅ {scraper}")

if __name__ == '__main__':
    cli()
