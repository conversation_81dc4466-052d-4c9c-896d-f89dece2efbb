"""Streamlit dashboard for Toyota 4Runner listing analysis."""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta

from analytics import AnalyticsEngine
from models import get_db_session, CarListing

# Page configuration
st.set_page_config(
    page_title="Toyota 4Runner Analyzer",
    page_icon="🚗",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize analytics engine
@st.cache_resource
def get_analytics_engine():
    return AnalyticsEngine()

def main():
    st.title("🚗 Toyota 4Runner V6 Market Analyzer")
    st.markdown("*San Jose, CA Area - Real-time market analysis*")
    
    analytics = get_analytics_engine()
    
    # Sidebar filters
    st.sidebar.header("🔍 Filters")
    
    # Get data for filters
    df = get_cached_data(analytics)
    
    if df.empty:
        st.error("❌ No data available. Please run the scraper first using: `python cli.py scrape`")
        return
    
    # Year filter
    min_year, max_year = int(df['year'].min()), int(df['year'].max())
    year_range = st.sidebar.slider(
        "Year Range", 
        min_value=min_year, 
        max_value=max_year, 
        value=(min_year, max_year)
    )
    
    # Price filter
    min_price, max_price = int(df['price'].min()), int(df['price'].max())
    price_range = st.sidebar.slider(
        "Price Range ($)", 
        min_value=min_price, 
        max_value=max_price, 
        value=(min_price, max_price),
        step=1000
    )
    
    # Seller type filter
    seller_types = ['All'] + list(df['seller_type'].unique())
    selected_seller = st.sidebar.selectbox("Seller Type", seller_types)
    
    # Platform filter
    platforms = ['All'] + list(df['platform'].unique())
    selected_platform = st.sidebar.selectbox("Platform", platforms)
    
    # Apply filters
    filtered_df = apply_filters(df, year_range, price_range, selected_seller, selected_platform)
    
    # Main dashboard
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Listings", len(filtered_df))
    
    with col2:
        avg_price = filtered_df['price'].mean()
        st.metric("Average Price", f"${avg_price:,.0f}")
    
    with col3:
        median_price = filtered_df['price'].median()
        st.metric("Median Price", f"${median_price:,.0f}")
    
    with col4:
        if not filtered_df['mileage'].isna().all():
            avg_mileage = filtered_df['mileage'].mean()
            st.metric("Average Mileage", f"{avg_mileage:,.0f}")
        else:
            st.metric("Average Mileage", "N/A")
    
    # Charts
    st.header("📊 Market Analysis")
    
    # Price trend by year
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("💰 Price by Year")
        year_stats = filtered_df.groupby('year')['price'].agg(['mean', 'count']).reset_index()
        
        fig_year = px.bar(
            year_stats, 
            x='year', 
            y='mean',
            title="Average Price by Year",
            labels={'mean': 'Average Price ($)', 'year': 'Year'}
        )
        fig_year.update_layout(showlegend=False)
        st.plotly_chart(fig_year, use_container_width=True)
    
    with col2:
        st.subheader("📈 Price vs Mileage")
        # Filter out null mileage for scatter plot
        scatter_df = filtered_df.dropna(subset=['mileage'])
        
        if not scatter_df.empty:
            fig_scatter = px.scatter(
                scatter_df,
                x='mileage',
                y='price',
                color='year',
                title="Price vs Mileage",
                labels={'mileage': 'Mileage', 'price': 'Price ($)'},
                hover_data=['year', 'seller_type', 'platform']
            )
            st.plotly_chart(fig_scatter, use_container_width=True)
        else:
            st.info("No mileage data available for scatter plot")
    
    # Seller type and platform distribution
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("👥 Seller Type Distribution")
        seller_counts = filtered_df['seller_type'].value_counts()
        
        fig_seller = px.pie(
            values=seller_counts.values,
            names=seller_counts.index,
            title="Dealer vs Private Sellers"
        )
        st.plotly_chart(fig_seller, use_container_width=True)
    
    with col2:
        st.subheader("🌐 Platform Distribution")
        platform_counts = filtered_df['platform'].value_counts()
        
        fig_platform = px.bar(
            x=platform_counts.index,
            y=platform_counts.values,
            title="Listings by Platform",
            labels={'x': 'Platform', 'y': 'Number of Listings'}
        )
        st.plotly_chart(fig_platform, use_container_width=True)
    
    # Best deals section
    st.header("💰 Best Deals")
    
    deals = analytics.find_best_deals(20)
    if deals:
        deals_df = pd.DataFrame(deals)
        
        # Display top deals
        st.subheader("🔥 Top Deals (Below Market Average)")
        
        for i, deal in enumerate(deals[:5]):
            with st.expander(f"Deal #{i+1}: {deal['year']} 4Runner - ${deal['price']:,.0f} (Save ${deal['savings']:,.0f})"):
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write(f"**Year:** {deal['year']}")
                    st.write(f"**Price:** ${deal['price']:,.0f}")
                    st.write(f"**Mileage:** {deal['mileage']:,} miles" if deal['mileage'] else "**Mileage:** N/A")
                    st.write(f"**Location:** {deal['location']}")
                
                with col2:
                    st.write(f"**Market Average:** ${deal['market_avg']:,.0f}")
                    st.write(f"**Your Savings:** ${deal['savings']:,.0f}")
                    st.write(f"**Deal Score:** {deal['deal_score']*100:.1f}% below market")
                    st.write(f"**Seller:** {deal['seller_type'].title()}")
                
                st.write(f"**Title:** {deal['title']}")
                st.write(f"**Link:** [View Listing]({deal['listing_url']})")
    else:
        st.info("No deals found below market average")
    
    # Recent listings
    st.header("🆕 Recent Listings")
    
    recent_df = filtered_df.sort_values('scraped_at', ascending=False).head(10)
    
    # Display recent listings in a nice format
    for _, listing in recent_df.iterrows():
        with st.container():
            col1, col2, col3 = st.columns([3, 2, 1])
            
            with col1:
                st.write(f"**{listing['year']} Toyota 4Runner**")
                st.write(f"{listing['title']}")
                if listing['location']:
                    st.write(f"📍 {listing['location']}")
            
            with col2:
                st.write(f"**${listing['price']:,.0f}**")
                if pd.notna(listing['mileage']):
                    st.write(f"{listing['mileage']:,} miles")
                st.write(f"*{listing['seller_type'].title()} on {listing['platform']}*")
            
            with col3:
                st.link_button("View Listing", listing['listing_url'])
            
            st.divider()

@st.cache_data(ttl=300)  # Cache for 5 minutes
def get_cached_data(analytics):
    """Get cached listing data."""
    return analytics.get_active_listings_df()

def apply_filters(df, year_range, price_range, seller_type, platform):
    """Apply filters to the dataframe."""
    filtered_df = df.copy()
    
    # Year filter
    filtered_df = filtered_df[
        (filtered_df['year'] >= year_range[0]) & 
        (filtered_df['year'] <= year_range[1])
    ]
    
    # Price filter
    filtered_df = filtered_df[
        (filtered_df['price'] >= price_range[0]) & 
        (filtered_df['price'] <= price_range[1])
    ]
    
    # Seller type filter
    if seller_type != 'All':
        filtered_df = filtered_df[filtered_df['seller_type'] == seller_type]
    
    # Platform filter
    if platform != 'All':
        filtered_df = filtered_df[filtered_df['platform'] == platform]
    
    return filtered_df

if __name__ == "__main__":
    main()
