"""Analytics engine for car listing data analysis."""

import pandas as pd
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
from sqlalchemy import func, and_

from models import CarListing, PriceHistory, get_db_session
from config import ANALYTICS_CONFIG

class AnalyticsEngine:
    """Engine for analyzing car listing data and identifying deals."""
    
    def __init__(self):
        self.db_session = get_db_session()
        self.mileage_buckets = ANALYTICS_CONFIG['mileage_buckets']
        self.deal_threshold = ANALYTICS_CONFIG['deal_threshold_percentage']
    
    def get_active_listings_df(self) -> pd.DataFrame:
        """Get active listings as a pandas DataFrame."""
        query = self.db_session.query(CarListing).filter(
            CarListing.is_active == True,
            CarListing.price.isnot(None),
            CarListing.year.isnot(None)
        )
        
        listings = query.all()
        data = [listing.to_dict() for listing in listings]
        
        if not data:
            return pd.DataFrame()
        
        df = pd.DataFrame(data)
        
        # Add mileage bucket
        df['mileage_bucket'] = df['mileage'].apply(self.get_mileage_bucket)
        
        return df
    
    def get_mileage_bucket(self, mileage: Optional[int]) -> str:
        """Categorize mileage into buckets."""
        if mileage is None:
            return 'Unknown'
        
        for min_miles, max_miles, bucket_name in self.mileage_buckets:
            if min_miles <= mileage <= max_miles:
                return bucket_name
        
        return 'Unknown'
    
    def get_summary_stats(self) -> Dict:
        """Get summary statistics for all active listings."""
        df = self.get_active_listings_df()
        
        if df.empty:
            return {'error': 'No active listings found'}
        
        stats = {
            'total_listings': len(df),
            'price_stats': {
                'mean': df['price'].mean(),
                'median': df['price'].median(),
                'min': df['price'].min(),
                'max': df['price'].max(),
                'std': df['price'].std()
            },
            'year_range': {
                'min': int(df['year'].min()),
                'max': int(df['year'].max())
            },
            'mileage_stats': {
                'mean': df['mileage'].mean() if df['mileage'].notna().any() else None,
                'median': df['mileage'].median() if df['mileage'].notna().any() else None,
                'min': df['mileage'].min() if df['mileage'].notna().any() else None,
                'max': df['mileage'].max() if df['mileage'].notna().any() else None
            },
            'platforms': df['platform'].value_counts().to_dict(),
            'seller_types': df['seller_type'].value_counts().to_dict()
        }
        
        return stats
    
    def get_price_by_year(self) -> Dict:
        """Get average price grouped by year."""
        df = self.get_active_listings_df()
        
        if df.empty:
            return {}
        
        year_stats = df.groupby('year')['price'].agg(['mean', 'median', 'count']).round(2)
        return year_stats.to_dict('index')
    
    def get_price_by_mileage_bucket(self) -> Dict:
        """Get average price grouped by mileage bucket."""
        df = self.get_active_listings_df()
        
        if df.empty:
            return {}
        
        mileage_stats = df.groupby('mileage_bucket')['price'].agg(['mean', 'median', 'count']).round(2)
        return mileage_stats.to_dict('index')
    
    def get_price_by_seller_type(self) -> Dict:
        """Get average price grouped by seller type."""
        df = self.get_active_listings_df()
        
        if df.empty:
            return {}
        
        seller_stats = df.groupby('seller_type')['price'].agg(['mean', 'median', 'count']).round(2)
        return seller_stats.to_dict('index')
    
    def find_best_deals(self, limit: int = 10) -> List[Dict]:
        """Find listings that are priced below market average."""
        df = self.get_active_listings_df()
        
        if df.empty:
            return []
        
        deals = []
        
        # Calculate market averages by year and mileage bucket
        year_averages = df.groupby('year')['price'].mean()
        mileage_averages = df.groupby('mileage_bucket')['price'].mean()
        
        for _, listing in df.iterrows():
            year_avg = year_averages.get(listing['year'])
            mileage_avg = mileage_averages.get(listing['mileage_bucket'])
            
            # Use year average as primary comparison
            if year_avg and listing['price'] < (year_avg * self.deal_threshold):
                deal_score = (year_avg - listing['price']) / year_avg
                
                deals.append({
                    'listing_id': listing['id'],
                    'title': listing['title'],
                    'year': listing['year'],
                    'price': listing['price'],
                    'mileage': listing['mileage'],
                    'location': listing['location'],
                    'seller_type': listing['seller_type'],
                    'platform': listing['platform'],
                    'listing_url': listing['listing_url'],
                    'market_avg': year_avg,
                    'savings': year_avg - listing['price'],
                    'deal_score': deal_score,
                    'scraped_at': listing['scraped_at']
                })
        
        # Sort by deal score (highest savings percentage first)
        deals.sort(key=lambda x: x['deal_score'], reverse=True)
        
        return deals[:limit]
    
    def get_price_trends(self, days: int = 30) -> List[Dict]:
        """Get price trends over time."""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        query = self.db_session.query(PriceHistory).filter(
            PriceHistory.changed_at >= cutoff_date,
            PriceHistory.price_change.isnot(None)
        ).order_by(PriceHistory.changed_at.desc())
        
        price_changes = query.all()
        
        trends = []
        for change in price_changes:
            trends.append({
                'vin': change.vin,
                'listing_url': change.listing_url,
                'old_price': change.old_price,
                'new_price': change.new_price,
                'price_change': change.price_change,
                'change_percentage': change.change_percentage,
                'changed_at': change.changed_at,
                'platform': change.platform
            })
        
        return trends
    
    def export_to_csv(self, filename: str = None) -> str:
        """Export active listings to CSV file."""
        df = self.get_active_listings_df()
        
        if df.empty:
            raise ValueError("No data to export")
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"4runner_listings_{timestamp}.csv"
        
        # Select relevant columns for export
        export_columns = [
            'year', 'price', 'mileage', 'title', 'location', 
            'seller_type', 'platform', 'listing_url', 'scraped_at'
        ]
        
        export_df = df[export_columns].copy()
        export_df.to_csv(filename, index=False)
        
        return filename
    
    def export_to_excel(self, filename: str = None) -> str:
        """Export analytics data to Excel file with multiple sheets."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"4runner_analytics_{timestamp}.xlsx"
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # Active listings
            df = self.get_active_listings_df()
            if not df.empty:
                export_columns = [
                    'year', 'price', 'mileage', 'title', 'location', 
                    'seller_type', 'platform', 'listing_url', 'scraped_at'
                ]
                df[export_columns].to_excel(writer, sheet_name='Active Listings', index=False)
            
            # Best deals
            deals = self.find_best_deals(50)
            if deals:
                deals_df = pd.DataFrame(deals)
                deals_df.to_excel(writer, sheet_name='Best Deals', index=False)
            
            # Summary stats
            stats = self.get_summary_stats()
            if 'error' not in stats:
                # Create a summary sheet
                summary_data = []
                summary_data.append(['Total Listings', stats['total_listings']])
                summary_data.append(['Average Price', f"${stats['price_stats']['mean']:,.0f}"])
                summary_data.append(['Median Price', f"${stats['price_stats']['median']:,.0f}"])
                summary_data.append(['Price Range', f"${stats['price_stats']['min']:,.0f} - ${stats['price_stats']['max']:,.0f}"])
                
                summary_df = pd.DataFrame(summary_data, columns=['Metric', 'Value'])
                summary_df.to_excel(writer, sheet_name='Summary', index=False)
        
        return filename
    
    def close(self):
        """Close database session."""
        self.db_session.close()
