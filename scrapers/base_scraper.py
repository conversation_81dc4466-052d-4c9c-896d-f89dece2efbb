"""Base scraper class for all platform scrapers."""

import time
import requests
from abc import ABC, abstractmethod
from typing import List, Dict, Optional
from datetime import datetime
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

from config import SCRAPING_CONFIG, SEARCH_CONFIG
from models import CarListing, get_db_session, ScrapingLog

class BaseScraper(ABC):
    """Abstract base class for all car listing scrapers."""
    
    def __init__(self, platform_name: str):
        self.platform_name = platform_name
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': SCRAPING_CONFIG['user_agent']
        })
        self.driver = None
        
    def setup_selenium(self):
        """Setup Selenium WebDriver for JavaScript-heavy sites."""
        if self.driver is None:
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument(f'--user-agent={SCRAPING_CONFIG["user_agent"]}')
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
    def cleanup_selenium(self):
        """Clean up Selenium WebDriver."""
        if self.driver:
            self.driver.quit()
            self.driver = None
    
    def make_request(self, url: str, params: Dict = None) -> Optional[requests.Response]:
        """Make HTTP request with error handling and rate limiting."""
        try:
            time.sleep(SCRAPING_CONFIG['request_delay'])
            response = self.session.get(
                url, 
                params=params,
                timeout=SCRAPING_CONFIG['timeout']
            )
            response.raise_for_status()
            return response
        except requests.RequestException as e:
            print(f"Request failed for {url}: {e}")
            return None
    
    def parse_price(self, price_text: str) -> Optional[float]:
        """Extract numeric price from text."""
        if not price_text:
            return None
        
        # Remove common price prefixes/suffixes and non-numeric characters
        import re
        price_clean = re.sub(r'[^\d.]', '', price_text.replace(',', ''))
        
        try:
            return float(price_clean)
        except ValueError:
            return None
    
    def parse_mileage(self, mileage_text: str) -> Optional[int]:
        """Extract numeric mileage from text."""
        if not mileage_text:
            return None
        
        import re
        # Look for numbers followed by common mileage indicators
        match = re.search(r'(\d{1,3}(?:,\d{3})*)\s*(?:miles?|mi|k)?', mileage_text.lower())
        if match:
            mileage_str = match.group(1).replace(',', '')
            try:
                mileage = int(mileage_str)
                # Handle 'k' notation (e.g., "120k" = 120,000)
                if 'k' in mileage_text.lower() and mileage < 1000:
                    mileage *= 1000
                return mileage
            except ValueError:
                pass
        return None
    
    def parse_year(self, year_text: str) -> Optional[int]:
        """Extract year from text."""
        if not year_text:
            return None
        
        import re
        # Look for 4-digit year
        match = re.search(r'(19|20)\d{2}', year_text)
        if match:
            try:
                year = int(match.group())
                if SEARCH_CONFIG['min_year'] <= year <= SEARCH_CONFIG['max_year']:
                    return year
            except ValueError:
                pass
        return None
    
    def is_valid_listing(self, listing_data: Dict) -> bool:
        """Validate if listing meets our criteria."""
        # Check required fields
        if not all([
            listing_data.get('price'),
            listing_data.get('year'),
            listing_data.get('listing_url')
        ]):
            return False
        
        # Check year range
        year = listing_data.get('year')
        if year and (year < SEARCH_CONFIG['min_year'] or year > SEARCH_CONFIG['max_year']):
            return False
        
        # Check if it's actually a 4Runner (sometimes other models slip through)
        title = (listing_data.get('title', '') + ' ' + listing_data.get('description', '')).lower()
        if '4runner' not in title and '4-runner' not in title:
            return False
        
        return True
    
    def save_listing(self, listing_data: Dict, db_session) -> Optional[CarListing]:
        """Save listing to database."""
        try:
            # Check if listing already exists (by URL)
            existing = db_session.query(CarListing).filter_by(
                listing_url=listing_data['listing_url']
            ).first()
            
            if existing:
                # Update existing listing
                for key, value in listing_data.items():
                    if hasattr(existing, key) and value is not None:
                        setattr(existing, key, value)
                existing.last_seen = datetime.utcnow()
                existing.is_active = True
                db_session.commit()
                return existing
            else:
                # Create new listing
                listing = CarListing(**listing_data)
                db_session.add(listing)
                db_session.commit()
                return listing
                
        except Exception as e:
            print(f"Error saving listing: {e}")
            db_session.rollback()
            return None
    
    @abstractmethod
    def scrape_listings(self) -> List[Dict]:
        """Scrape listings from the platform. Must be implemented by subclasses."""
        pass
    
    def run_scraper(self) -> Dict:
        """Run the scraper and return results summary."""
        db_session = get_db_session()
        
        # Create scraping log
        log = ScrapingLog(platform=self.platform_name)
        db_session.add(log)
        db_session.commit()
        
        try:
            print(f"Starting scraper for {self.platform_name}...")
            listings_data = self.scrape_listings()
            
            new_count = 0
            updated_count = 0
            error_count = 0
            
            for listing_data in listings_data:
                if self.is_valid_listing(listing_data):
                    existing = db_session.query(CarListing).filter_by(
                        listing_url=listing_data['listing_url']
                    ).first()
                    
                    saved_listing = self.save_listing(listing_data, db_session)
                    if saved_listing:
                        if existing:
                            updated_count += 1
                        else:
                            new_count += 1
                    else:
                        error_count += 1
                else:
                    error_count += 1
            
            # Update log
            log.completed_at = datetime.utcnow()
            log.listings_found = len(listings_data)
            log.new_listings = new_count
            log.updated_listings = updated_count
            log.errors_count = error_count
            log.status = 'completed'
            db_session.commit()
            
            results = {
                'platform': self.platform_name,
                'listings_found': len(listings_data),
                'new_listings': new_count,
                'updated_listings': updated_count,
                'errors': error_count,
                'status': 'success'
            }
            
            print(f"Scraper completed: {results}")
            return results
            
        except Exception as e:
            # Update log with error
            log.completed_at = datetime.utcnow()
            log.status = 'failed'
            log.error_message = str(e)
            db_session.commit()
            
            print(f"Scraper failed for {self.platform_name}: {e}")
            return {
                'platform': self.platform_name,
                'status': 'error',
                'error': str(e)
            }
        finally:
            self.cleanup_selenium()
            db_session.close()
