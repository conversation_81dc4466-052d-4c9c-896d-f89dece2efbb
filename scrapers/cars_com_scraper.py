"""Cars.com scraper for Toyota 4Runner listings."""

import re
import json
from typing import List, Dict
from urllib.parse import urljoin
from bs4 import BeautifulSoup

from .base_scraper import BaseScraper
from config import PLATFORMS

class CarsComScraper(BaseScraper):
    """Scraper for Cars.com car listings."""
    
    def __init__(self):
        super().__init__('cars_com')
        self.base_url = PLATFORMS['cars_com']['base_url']
        self.search_params = PLATFORMS['cars_com']['search_params']
    
    def build_search_url(self) -> str:
        """Build the search URL for Cars.com."""
        return self.base_url
    
    def scrape_listings(self) -> List[Dict]:
        """Scrape listings from Cars.com using Selenium for JavaScript content."""
        listings = []
        
        try:
            self.setup_selenium()
            
            # Build search URL with parameters
            search_url = f"{self.base_url}?make_model_list[]=toyota-4runner&zip=95110&maximum_distance=50"
            
            print(f"Scraping Cars.com: {search_url}")
            self.driver.get(search_url)
            
            # Wait for listings to load
            import time
            time.sleep(5)
            
            # Get page source and parse with BeautifulSoup
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Find listing cards
            listing_cards = soup.find_all('div', class_='vehicle-card')
            
            for card in listing_cards:
                try:
                    listing_data = self.parse_listing_card(card)
                    if listing_data:
                        listings.append(listing_data)
                except Exception as e:
                    print(f"Error parsing Cars.com listing: {e}")
                    continue
            
            print(f"Found {len(listings)} listings on Cars.com")
            
        except Exception as e:
            print(f"Error scraping Cars.com: {e}")
        
        return listings
    
    def parse_listing_card(self, card) -> Dict:
        """Parse individual listing card from Cars.com."""
        listing_data = {
            'platform': self.platform_name,
            'make': 'Toyota',
            'model': '4Runner'
        }
        
        # Get title and basic info
        title_elem = card.find('h2', class_='title')
        if title_elem:
            title_link = title_elem.find('a')
            if title_link:
                listing_data['title'] = title_link.get_text(strip=True)
                listing_data['listing_url'] = urljoin('https://www.cars.com', title_link.get('href', ''))
        
        # Get price
        price_elem = card.find('span', class_='primary-price')
        if price_elem:
            price_text = price_elem.get_text(strip=True)
            listing_data['price'] = self.parse_price(price_text)
        
        # Get mileage
        mileage_elem = card.find('div', class_='mileage')
        if mileage_elem:
            mileage_text = mileage_elem.get_text(strip=True)
            listing_data['mileage'] = self.parse_mileage(mileage_text)
        
        # Get year from title
        title = listing_data.get('title', '')
        year = self.parse_year(title)
        if year:
            listing_data['year'] = year
        
        # Get location
        location_elem = card.find('div', class_='miles-from')
        if location_elem:
            listing_data['location'] = location_elem.get_text(strip=True)
        
        # Get dealer info
        dealer_elem = card.find('div', class_='dealer-name')
        if dealer_elem:
            listing_data['seller_type'] = 'dealer'
            listing_data['contact_info'] = dealer_elem.get_text(strip=True)
        else:
            listing_data['seller_type'] = 'private'
        
        # Get image
        img_elem = card.find('img', class_='vehicle-image')
        if img_elem:
            img_src = img_elem.get('src') or img_elem.get('data-src')
            if img_src:
                listing_data['image_urls'] = json.dumps([img_src])
        
        return listing_data
