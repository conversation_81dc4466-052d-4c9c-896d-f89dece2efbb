"""Craigslist scraper for Toyota 4Runner listings."""

import re
from typing import List, Dict
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup

from .base_scraper import BaseScraper
from config import PLATFORMS

class CraigslistScraper(BaseScraper):
    """Scraper for Craigslist car listings."""
    
    def __init__(self):
        super().__init__('craigslist')
        self.base_url = PLATFORMS['craigslist']['base_url']
        self.search_params = PLATFORMS['craigslist']['search_params']
    
    def build_search_url(self) -> str:
        """Build the search URL for Craigslist."""
        return self.base_url
    
    def scrape_listings(self) -> List[Dict]:
        """Scrape listings from Craigslist."""
        listings = []
        
        try:
            # Get search results page
            response = self.make_request(self.build_search_url(), self.search_params)
            if not response:
                return listings
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find listing items
            listing_items = soup.find_all('li', class_='result-row')
            
            for item in listing_items:
                try:
                    listing_data = self.parse_listing_item(item)
                    if listing_data:
                        listings.append(listing_data)
                except Exception as e:
                    print(f"Error parsing Craigslist listing: {e}")
                    continue
            
            print(f"Found {len(listings)} listings on Craigslist")
            
        except Exception as e:
            print(f"Error scraping Craigslist: {e}")
        
        return listings
    
    def parse_listing_item(self, item) -> Dict:
        """Parse individual listing item from Craigslist."""
        listing_data = {
            'platform': self.platform_name,
            'make': 'Toyota',
            'model': '4Runner'
        }
        
        # Get title and URL
        title_link = item.find('a', class_='result-title')
        if title_link:
            listing_data['title'] = title_link.get_text(strip=True)
            listing_data['listing_url'] = urljoin(self.base_url, title_link.get('href', ''))
        
        # Get price
        price_elem = item.find('span', class_='result-price')
        if price_elem:
            price_text = price_elem.get_text(strip=True)
            listing_data['price'] = self.parse_price(price_text)
        
        # Get location
        location_elem = item.find('span', class_='result-hood')
        if location_elem:
            listing_data['location'] = location_elem.get_text(strip=True).strip('()')
        
        # Extract year and mileage from title
        title = listing_data.get('title', '')
        
        # Extract year
        year = self.parse_year(title)
        if year:
            listing_data['year'] = year
        
        # Extract mileage
        mileage = self.parse_mileage(title)
        if mileage:
            listing_data['mileage'] = mileage
        
        # Determine seller type (Craigslist is mostly private sellers)
        listing_data['seller_type'] = 'private'
        
        # Get additional details by visiting the listing page
        if listing_data.get('listing_url'):
            detailed_data = self.get_listing_details(listing_data['listing_url'])
            listing_data.update(detailed_data)
        
        return listing_data
    
    def get_listing_details(self, listing_url: str) -> Dict:
        """Get additional details from individual listing page."""
        details = {}
        
        try:
            response = self.make_request(listing_url)
            if not response:
                return details
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Get description
            description_elem = soup.find('section', id='postingbody')
            if description_elem:
                details['description'] = description_elem.get_text(strip=True)
            
            # Look for VIN in description
            description_text = details.get('description', '')
            vin_match = re.search(r'\b[A-HJ-NPR-Z0-9]{17}\b', description_text.upper())
            if vin_match:
                details['vin'] = vin_match.group()
            
            # Get attributes (mileage, year, etc.)
            attr_groups = soup.find_all('p', class_='attrgroup')
            for group in attr_groups:
                spans = group.find_all('span')
                for span in spans:
                    text = span.get_text(strip=True).lower()
                    
                    # Look for odometer/mileage
                    if 'odometer:' in text:
                        mileage_text = text.replace('odometer:', '').strip()
                        mileage = self.parse_mileage(mileage_text)
                        if mileage:
                            details['mileage'] = mileage
                    
                    # Look for year
                    elif any(keyword in text for keyword in ['year:', 'model year:']):
                        year_text = re.sub(r'(year:|model year:)', '', text).strip()
                        year = self.parse_year(year_text)
                        if year:
                            details['year'] = year
            
            # Get images
            image_elements = soup.find_all('img')
            image_urls = []
            for img in image_elements:
                src = img.get('src')
                if src and 'images.craigslist.org' in src:
                    image_urls.append(src)
            
            if image_urls:
                import json
                details['image_urls'] = json.dumps(image_urls)
            
        except Exception as e:
            print(f"Error getting Craigslist listing details: {e}")
        
        return details
