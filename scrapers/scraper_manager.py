"""Manager for coordinating multiple scrapers."""

from typing import List, Dict
from concurrent.futures import ThreadPoolExecutor, as_completed

from .craigslist_scraper import Craigslist<PERSON>craper
from .cars_com_scraper import CarsComScraper
from config import PLATFORMS

class ScraperManager:
    """Manages multiple scrapers and coordinates their execution."""
    
    def __init__(self):
        self.scrapers = {}
        self.initialize_scrapers()
    
    def initialize_scrapers(self):
        """Initialize all enabled scrapers."""
        if PLATFORMS['craigslist']['enabled']:
            self.scrapers['craigslist'] = CraigslistScraper
        
        if PLATFORMS['cars_com']['enabled']:
            self.scrapers['cars_com'] = CarsComScraper
        
        # Add more scrapers here as they're implemented
        # if PLATFORMS['autotrader']['enabled']:
        #     self.scrapers['autotrader'] = AutotraderScraper
    
    def run_single_scraper(self, platform_name: str) -> Dict:
        """Run a single scraper."""
        if platform_name not in self.scrapers:
            return {
                'platform': platform_name,
                'status': 'error',
                'error': f'Scraper not found or not enabled for {platform_name}'
            }
        
        scraper_class = self.scrapers[platform_name]
        scraper = scraper_class()
        return scraper.run_scraper()
    
    def run_all_scrapers(self, parallel: bool = True) -> List[Dict]:
        """Run all enabled scrapers."""
        results = []
        
        if parallel and len(self.scrapers) > 1:
            # Run scrapers in parallel
            with ThreadPoolExecutor(max_workers=3) as executor:
                future_to_platform = {
                    executor.submit(self.run_single_scraper, platform): platform
                    for platform in self.scrapers.keys()
                }
                
                for future in as_completed(future_to_platform):
                    platform = future_to_platform[future]
                    try:
                        result = future.result()
                        results.append(result)
                    except Exception as e:
                        results.append({
                            'platform': platform,
                            'status': 'error',
                            'error': str(e)
                        })
        else:
            # Run scrapers sequentially
            for platform_name in self.scrapers.keys():
                result = self.run_single_scraper(platform_name)
                results.append(result)
        
        return results
    
    def get_scraper_status(self) -> Dict:
        """Get status of all scrapers."""
        return {
            'enabled_scrapers': list(self.scrapers.keys()),
            'total_scrapers': len(self.scrapers),
            'available_platforms': list(PLATFORMS.keys())
        }
