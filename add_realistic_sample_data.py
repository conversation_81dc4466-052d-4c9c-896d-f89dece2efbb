#!/usr/bin/env python3
"""Add realistic sample data with actual-looking URLs for testing."""

from datetime import datetime, timedelta
import random
from models import CarListing, get_db_session, init_database

def add_realistic_sample_listings():
    """Add realistic Toyota 4Runner listings with real-looking URLs."""
    
    # Initialize database
    init_database()
    db_session = get_db_session()
    
    # Clear existing sample data
    db_session.query(CarListing).filter(CarListing.listing_url.like('%example.com%')).delete()
    db_session.commit()
    
    # Realistic sample data with actual-looking URLs
    realistic_listings = [
        {
            'year': 2020,
            'price': 35000,
            'mileage': 45000,
            'title': '2020 Toyota 4Runner SR5 V6 4WD - Excellent Condition',
            'location': 'San Jose, CA',
            'seller_type': 'dealer',
            'platform': 'cars_com',
            'listing_url': 'https://www.cars.com/vehicledetail/detail/123456789/overview/',
            'description': 'Clean Carfax, one owner, well maintained 4Runner with low miles.'
        },
        {
            'year': 2018,
            'price': 28000,
            'mileage': 75000,
            'title': '2018 Toyota 4Runner TRD Off-Road V6 - Trail Ready',
            'location': 'Santa Clara, CA',
            'seller_type': 'private',
            'platform': 'craigslist',
            'listing_url': 'https://sfbay.craigslist.org/sby/cto/d/2018-toyota-4runner-trd-off-road/7654321.html',
            'description': 'Great off-road vehicle, new tires, recent maintenance.'
        },
        {
            'year': 2019,
            'price': 32000,
            'mileage': 55000,
            'title': '2019 Toyota 4Runner Limited V6 AWD - Loaded',
            'location': 'Fremont, CA',
            'seller_type': 'dealer',
            'platform': 'cars_com',
            'listing_url': 'https://www.cars.com/vehicledetail/detail/987654321/overview/',
            'description': 'Fully loaded Limited trim with leather, navigation, and premium sound.'
        },
        {
            'year': 2017,
            'price': 25000,
            'mileage': 95000,
            'title': '2017 Toyota 4Runner SR5 Premium V6 - Great Deal',
            'location': 'Sunnyvale, CA',
            'seller_type': 'private',
            'platform': 'craigslist',
            'listing_url': 'https://sfbay.craigslist.org/pen/cto/d/2017-toyota-4runner-sr5-premium/7123456.html',
            'description': 'Well maintained, service records available, clean title.'
        },
        {
            'year': 2021,
            'price': 42000,
            'mileage': 25000,
            'title': '2021 Toyota 4Runner TRD Pro V6 4WD - Like New',
            'location': 'Mountain View, CA',
            'seller_type': 'dealer',
            'platform': 'autotrader',
            'listing_url': 'https://www.autotrader.com/cars-for-sale/vehicledetails.xhtml?listingId=654789123',
            'description': 'Rare TRD Pro model with all off-road upgrades, low miles.'
        },
        {
            'year': 2016,
            'price': 22000,
            'mileage': 120000,
            'title': '2016 Toyota 4Runner Trail Edition V6 - Adventure Ready',
            'location': 'Palo Alto, CA',
            'seller_type': 'private',
            'platform': 'craigslist',
            'listing_url': 'https://sfbay.craigslist.org/pen/cto/d/2016-toyota-4runner-trail-edition/7987654.html',
            'description': 'Perfect for outdoor adventures, well maintained, new brakes.'
        },
        {
            'year': 2022,
            'price': 45000,
            'mileage': 15000,
            'title': '2022 Toyota 4Runner SR5 V6 Premium - Almost New',
            'location': 'Milpitas, CA',
            'seller_type': 'dealer',
            'platform': 'cars_com',
            'listing_url': 'https://www.cars.com/vehicledetail/detail/456789123/overview/',
            'description': 'Nearly new 4Runner with warranty remaining, pristine condition.'
        },
        {
            'year': 2015,
            'price': 19500,
            'mileage': 145000,
            'title': '2015 Toyota 4Runner SR5 V6 - High Miles, Great Price',
            'location': 'San Mateo, CA',
            'seller_type': 'private',
            'platform': 'craigslist',
            'listing_url': 'https://sfbay.craigslist.org/pen/cto/d/2015-toyota-4runner-sr5-high-miles/7456123.html',
            'description': 'High mileage but runs great, recent timing belt service.'
        }
    ]
    
    # Add more variety with realistic URLs
    years = [2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022, 2023]
    locations = ['San Jose, CA', 'Santa Clara, CA', 'Sunnyvale, CA', 'Mountain View, CA', 
                'Palo Alto, CA', 'Fremont, CA', 'Milpitas, CA', 'Cupertino, CA']
    
    # URL templates for different platforms
    url_templates = {
        'craigslist': 'https://sfbay.craigslist.org/sby/cto/d/{year}-toyota-4runner-v6-{id}/7{id}.html',
        'cars_com': 'https://www.cars.com/vehicledetail/detail/{id}/overview/',
        'autotrader': 'https://www.autotrader.com/cars-for-sale/vehicledetails.xhtml?listingId={id}'
    }
    
    for i in range(15):  # Add 15 more realistic listings
        year = random.choice(years)
        base_price = 45000 - (2024 - year) * 2500  # Depreciation
        price_variation = random.randint(-5000, 5000)
        price = max(15000, base_price + price_variation)
        
        mileage = random.randint(10000, 150000)
        location = random.choice(locations)
        platform = random.choice(['craigslist', 'cars_com', 'autotrader'])
        seller_type = 'dealer' if platform in ['cars_com', 'autotrader'] else random.choice(['dealer', 'private'])
        
        # Generate realistic URL
        listing_id = random.randint(100000000, 999999999)
        url_template = url_templates[platform]
        listing_url = url_template.format(year=year, id=listing_id)
        
        listing = {
            'year': year,
            'price': price,
            'mileage': mileage,
            'title': f'{year} Toyota 4Runner V6 - {random.choice(["Great Condition", "Well Maintained", "Low Miles", "Clean Title", "One Owner"])}',
            'location': location,
            'seller_type': seller_type,
            'platform': platform,
            'listing_url': listing_url,
            'description': f'Great {year} 4Runner with {mileage:,} miles. {random.choice(["Recent maintenance", "Clean Carfax", "Service records available", "Well cared for"])}.',
            'scraped_at': datetime.utcnow() - timedelta(hours=random.randint(1, 48))
        }
        realistic_listings.append(listing)
    
    # Add listings to database
    added_count = 0
    for listing_data in realistic_listings:
        try:
            # Check if listing already exists
            existing = db_session.query(CarListing).filter_by(
                listing_url=listing_data['listing_url']
            ).first()
            
            if not existing:
                listing = CarListing(
                    make='Toyota',
                    model='4Runner',
                    **listing_data
                )
                db_session.add(listing)
                added_count += 1
        except Exception as e:
            print(f"Error adding listing: {e}")
    
    try:
        db_session.commit()
        print(f"✅ Added {added_count} realistic sample listings to the database!")
        print(f"📊 Total listings in database: {db_session.query(CarListing).count()}")
        print("\n🔗 Sample URLs that will be shown:")
        for i, listing in enumerate(realistic_listings[:3]):
            print(f"  {i+1}. {listing['platform']}: {listing['listing_url']}")
    except Exception as e:
        print(f"❌ Error committing to database: {e}")
        db_session.rollback()
    finally:
        db_session.close()

if __name__ == "__main__":
    add_realistic_sample_listings()
