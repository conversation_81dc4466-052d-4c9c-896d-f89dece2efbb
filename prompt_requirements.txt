You are a CLI-based intelligent coding agent that builds end-to-end applications.

Your task is to build a complete Python application that helps me search and analyze used Toyota 4Runner V6 cars for sale in the San Jose, California area.

## Functional Requirements:
1. **Car Model & Engine Filter**: Focus only on listings for "Toyota 4Runner" with a **V6 engine**.
2. **Location Filter**: Limit results to **San Jose, CA**, and nearby areas (e.g., within 50 miles).
3. **Scraper**:
   - Scrape listings **daily** from popular platforms (e.g., Craigslist, Facebook Marketplace, Autotrader, Cars.com, CarGurus, Edmunds).
   - Extract the following fields:
     - Price
     - Mileage
     - Year
     - Location
     - Seller type (Dealer or Private)
     - VIN (if available)
     - Listing URL
4. **Data Storage**:
   - Store scraped data in a local or cloud-based **SQLite or PostgreSQL database** with historical tracking.
   - Use timestamps to track daily data for price trend analysis.
5. **Analytics Engine**:
   - Compute average price grouped by:
     - Year
     - Mileage bucket (e.g., 0–50k, 50–100k, 100k+)
     - Seller type
   - Show best deals compared to market average.
   - Identify price drops for the same VIN over time.
6. **CLI Commands**:
   - `scrape` → runs the scraper manually
   - `analyze` → shows summary stats and deals
   - `dashboard` → launches a **local dashboard** (see below)
   - `export` → save analytics to CSV or Excel

## Dashboard:
- Use **Streamlit** or **Plotly Dash** for a simple local dashboard UI.
- Key visualizations:
  - **Line chart**: Price trend by Year
  - **Line chart**: Price vs Mileage
  - **Pie chart**: Dealer vs Private listings
  - **Bar chart**: Number of listings by year
- Include search/filter on year, price range, seller type.

## Other Notes:
- Schedule daily scraping using `cron` or `APScheduler`.
- Keep the system modular and well-documented.
- Optional: Include notification support (email or CLI alert) when a good deal (price below market average) is found.

## Output:
- A complete Python project with:
  - Modular scraper
  - SQLite/Postgres database integration
  - CLI interface using `argparse` or `Click`
  - Analytics engine with reusable functions
  - Streamlit or Plotly dashboard

Do not include any unnecessary dependencies or sample data. Start coding.

