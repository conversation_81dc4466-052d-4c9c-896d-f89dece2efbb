# Toyota 4Runner V6 Market Analyzer 🚗

A comprehensive Python application for scraping, analyzing, and tracking Toyota 4Runner V6 listings in the San Jose, California area. This tool helps you find the best deals by monitoring multiple platforms and providing detailed market analysis.

## Features

### 🔍 **Multi-Platform Scraping**
- **Craigslist**: Private seller listings
- **Cars.com**: Dealer and private listings
- **Autotrader**: Professional dealer network (coming soon)
- **Facebook Marketplace**: Social marketplace (coming soon)
- **CarGurus**: Price analysis platform (coming soon)

### 📊 **Advanced Analytics**
- Price trend analysis by year and mileage
- Market average calculations
- Best deal identification (below market average)
- Historical price tracking
- Seller type analysis (dealer vs private)

### 🖥️ **Interactive Dashboard**
- Real-time market visualization
- Price vs mileage scatter plots
- Year-over-year price trends
- Platform and seller distribution charts
- Advanced filtering and search

### 🤖 **Automation**
- Daily automated scraping
- Price change notifications
- Deal alerts when good opportunities arise
- Scheduled data exports

### 💾 **Data Management**
- SQLite database (default) or PostgreSQL
- Historical data tracking
- Export to CSV/Excel
- Data deduplication and validation

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/4runner-analyzer.git
cd 4runner-analyzer

# Install dependencies
pip install -r requirements.txt

# Initialize the database
python main.py init
```

### 2. First Scrape

```bash
# Run scrapers for all platforms
python main.py scrape

# Or scrape a specific platform
python main.py scrape --platform craigslist
```

### 3. View Analysis

```bash
# Show summary statistics
python main.py analyze

# Show best deals only
python main.py analyze --deals

# Show price trends
python main.py analyze --trends
```

### 4. Launch Dashboard

```bash
# Start the interactive dashboard
python main.py dashboard

# Then open http://localhost:8501 in your browser
```

## CLI Commands

### Core Commands

```bash
# Initialize database
python main.py init

# Run scrapers
python main.py scrape [--platform PLATFORM] [--parallel/--sequential]

# Analyze data
python main.py analyze [--deals] [--trends] [--limit N]

# Export data
python main.py export [--format csv|excel] [--filename FILE]

# Launch dashboard
python main.py dashboard [--port PORT]

# Show status
python main.py status
```

### Examples

```bash
# Scrape only Craigslist
python main.py scrape --platform craigslist

# Show top 20 deals
python main.py analyze --deals --limit 20

# Export to Excel
python main.py export --format excel --filename my_analysis.xlsx

# Run dashboard on port 8080
python main.py dashboard --port 8080
```

## Configuration

### Database Setup

By default, the application uses SQLite. For PostgreSQL:

1. Copy `.env.example` to `.env`
2. Update `DATABASE_URL`:
```
DATABASE_URL=postgresql://username:password@localhost:5432/4runner_db
```

### Search Parameters

Edit `config.py` to customize:

```python
SEARCH_CONFIG = {
    'make': 'Toyota',
    'model': '4Runner',
    'engine': 'V6',
    'location': 'San Jose, CA',
    'radius_miles': 50,
    'min_year': 2000,
    'max_year': 2024
}
```

### Platform Configuration

Enable/disable platforms in `config.py`:

```python
PLATFORMS = {
    'craigslist': {'enabled': True, ...},
    'cars_com': {'enabled': True, ...},
    'autotrader': {'enabled': False, ...}  # Coming soon
}
```

## Automated Scheduling

### Daily Scraping

```bash
# Start the scheduler (runs daily at 8 AM)
python scheduler.py
```

### Cron Setup (Alternative)

```bash
# Add to crontab for daily 8 AM scraping
0 8 * * * /path/to/python /path/to/main.py scrape
```

## Dashboard Features

The Streamlit dashboard provides:

- **📊 Market Overview**: Total listings, average prices, trends
- **🔍 Advanced Filters**: Year, price, seller type, platform
- **📈 Visualizations**:
  - Price by year bar charts
  - Price vs mileage scatter plots
  - Seller type pie charts
  - Platform distribution
- **💰 Deal Alerts**: Listings below market average
- **🆕 Recent Listings**: Latest finds with direct links

## Project Structure

```
4runner-analyzer/
├── main.py              # Main CLI entry point
├── cli.py               # Command-line interface
├── config.py            # Configuration settings
├── models.py            # Database models
├── analytics.py         # Analysis engine
├── dashboard.py         # Streamlit dashboard
├── scheduler.py         # Automated scheduling
├── scrapers/
│   ├── __init__.py
│   ├── base_scraper.py      # Base scraper class
│   ├── craigslist_scraper.py # Craigslist implementation
│   ├── cars_com_scraper.py   # Cars.com implementation
│   └── scraper_manager.py    # Scraper coordination
├── requirements.txt     # Python dependencies
├── .env.example        # Environment variables template
└── README.md           # This file
```