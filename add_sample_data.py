#!/usr/bin/env python3
"""Add sample data for testing the dashboard."""

from datetime import datetime, timedelta
import random
from models import CarListing, get_db_session, init_database

def add_sample_listings():
    """Add sample Toyota 4Runner listings for testing."""
    
    # Initialize database
    init_database()
    db_session = get_db_session()
    
    # Sample data
    sample_listings = [
        {
            'year': 2020,
            'price': 35000,
            'mileage': 45000,
            'title': '2020 Toyota 4Runner SR5 V6 4WD',
            'location': 'San Jose, CA',
            'seller_type': 'dealer',
            'platform': 'cars_com',
            'listing_url': 'https://example.com/listing1'
        },
        {
            'year': 2018,
            'price': 28000,
            'mileage': 75000,
            'title': '2018 Toyota 4Runner TRD Off-Road V6',
            'location': 'Santa Clara, CA',
            'seller_type': 'private',
            'platform': 'craigslist',
            'listing_url': 'https://example.com/listing2'
        },
        {
            'year': 2019,
            'price': 32000,
            'mileage': 55000,
            'title': '2019 Toyota 4Runner Limited V6 AWD',
            'location': 'Fremont, CA',
            'seller_type': 'dealer',
            'platform': 'cars_com',
            'listing_url': 'https://example.com/listing3'
        },
        {
            'year': 2017,
            'price': 25000,
            'mileage': 95000,
            'title': '2017 Toyota 4Runner SR5 Premium V6',
            'location': 'Sunnyvale, CA',
            'seller_type': 'private',
            'platform': 'craigslist',
            'listing_url': 'https://example.com/listing4'
        },
        {
            'year': 2021,
            'price': 42000,
            'mileage': 25000,
            'title': '2021 Toyota 4Runner TRD Pro V6 4WD',
            'location': 'Mountain View, CA',
            'seller_type': 'dealer',
            'platform': 'cars_com',
            'listing_url': 'https://example.com/listing5'
        },
        {
            'year': 2016,
            'price': 22000,
            'mileage': 120000,
            'title': '2016 Toyota 4Runner Trail Edition V6',
            'location': 'Palo Alto, CA',
            'seller_type': 'private',
            'platform': 'craigslist',
            'listing_url': 'https://example.com/listing6'
        },
        {
            'year': 2022,
            'price': 45000,
            'mileage': 15000,
            'title': '2022 Toyota 4Runner SR5 V6 Premium',
            'location': 'Milpitas, CA',
            'seller_type': 'dealer',
            'platform': 'cars_com',
            'listing_url': 'https://example.com/listing7'
        },
        {
            'year': 2015,
            'price': 19500,
            'mileage': 145000,
            'title': '2015 Toyota 4Runner SR5 V6 - Great Condition',
            'location': 'San Mateo, CA',
            'seller_type': 'private',
            'platform': 'craigslist',
            'listing_url': 'https://example.com/listing8'
        },
        {
            'year': 2020,
            'price': 38000,
            'mileage': 35000,
            'title': '2020 Toyota 4Runner TRD Off-Road V6',
            'location': 'Cupertino, CA',
            'seller_type': 'dealer',
            'platform': 'cars_com',
            'listing_url': 'https://example.com/listing9'
        },
        {
            'year': 2018,
            'price': 26500,
            'mileage': 85000,
            'title': '2018 Toyota 4Runner SR5 V6 - Clean Title',
            'location': 'Campbell, CA',
            'seller_type': 'private',
            'platform': 'craigslist',
            'listing_url': 'https://example.com/listing10'
        }
    ]
    
    # Add more variety with random data
    years = [2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022, 2023]
    locations = ['San Jose, CA', 'Santa Clara, CA', 'Sunnyvale, CA', 'Mountain View, CA', 
                'Palo Alto, CA', 'Fremont, CA', 'Milpitas, CA', 'Cupertino, CA']
    platforms = ['craigslist', 'cars_com']
    seller_types = ['dealer', 'private']
    
    for i in range(20):  # Add 20 more random listings
        year = random.choice(years)
        base_price = 45000 - (2024 - year) * 2500  # Depreciation
        price_variation = random.randint(-5000, 5000)
        price = max(15000, base_price + price_variation)
        
        mileage = random.randint(10000, 150000)
        location = random.choice(locations)
        platform = random.choice(platforms)
        seller_type = random.choice(seller_types)
        
        listing = {
            'year': year,
            'price': price,
            'mileage': mileage,
            'title': f'{year} Toyota 4Runner V6 - Sample Listing {i+11}',
            'location': location,
            'seller_type': seller_type,
            'platform': platform,
            'listing_url': f'https://example.com/listing{i+11}',
            'description': f'Great {year} 4Runner with {mileage:,} miles. Well maintained.',
            'scraped_at': datetime.utcnow() - timedelta(hours=random.randint(1, 48))
        }
        sample_listings.append(listing)
    
    # Add listings to database
    added_count = 0
    for listing_data in sample_listings:
        try:
            # Check if listing already exists
            existing = db_session.query(CarListing).filter_by(
                listing_url=listing_data['listing_url']
            ).first()
            
            if not existing:
                listing = CarListing(
                    make='Toyota',
                    model='4Runner',
                    **listing_data
                )
                db_session.add(listing)
                added_count += 1
        except Exception as e:
            print(f"Error adding listing: {e}")
    
    try:
        db_session.commit()
        print(f"✅ Added {added_count} sample listings to the database!")
        print(f"📊 Total listings in database: {db_session.query(CarListing).count()}")
    except Exception as e:
        print(f"❌ Error committing to database: {e}")
        db_session.rollback()
    finally:
        db_session.close()

if __name__ == "__main__":
    add_sample_listings()
