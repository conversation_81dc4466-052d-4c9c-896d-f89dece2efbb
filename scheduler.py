"""Scheduler for automated daily scraping."""

import logging
from datetime import datetime
from apscheduler.schedulers.blocking import BlockingScheduler
from apscheduler.triggers.cron import CronTrigger
import pytz

from scrapers.scraper_manager import Scraper<PERSON>anager
from config import SCHEDULER_CONFIG

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper_scheduler.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class ScrapingScheduler:
    """Scheduler for automated scraping tasks."""
    
    def __init__(self):
        self.scheduler = BlockingScheduler()
        self.scraper_manager = ScraperManager()
        self.timezone = pytz.timezone(SCHEDULER_CONFIG['timezone'])
    
    def scheduled_scrape_job(self):
        """Job function that runs the scrapers."""
        logger.info("Starting scheduled scraping job...")
        
        try:
            results = self.scraper_manager.run_all_scrapers(parallel=True)
            
            # Log results
            total_found = 0
            total_new = 0
            total_errors = 0
            
            for result in results:
                platform = result['platform']
                status = result['status']
                
                if status == 'success':
                    found = result.get('listings_found', 0)
                    new = result.get('new_listings', 0)
                    updated = result.get('updated_listings', 0)
                    
                    total_found += found
                    total_new += new
                    
                    logger.info(f"{platform}: {found} found, {new} new, {updated} updated")
                else:
                    total_errors += 1
                    error = result.get('error', 'Unknown error')
                    logger.error(f"{platform} failed: {error}")
            
            logger.info(f"Scraping completed - Total: {total_found} found, {total_new} new, {total_errors} errors")
            
        except Exception as e:
            logger.error(f"Scheduled scraping job failed: {e}")
    
    def add_daily_scraping_job(self):
        """Add daily scraping job to scheduler."""
        trigger = CronTrigger(
            hour=SCHEDULER_CONFIG['scrape_hour'],
            minute=SCHEDULER_CONFIG['scrape_minute'],
            timezone=self.timezone
        )
        
        self.scheduler.add_job(
            func=self.scheduled_scrape_job,
            trigger=trigger,
            id='daily_scrape',
            name='Daily Car Listing Scrape',
            replace_existing=True
        )
        
        logger.info(f"Daily scraping job scheduled for {SCHEDULER_CONFIG['scrape_hour']:02d}:{SCHEDULER_CONFIG['scrape_minute']:02d} {SCHEDULER_CONFIG['timezone']}")
    
    def start(self):
        """Start the scheduler."""
        self.add_daily_scraping_job()
        
        logger.info("Starting scheduler...")
        logger.info("Press Ctrl+C to stop the scheduler")
        
        try:
            self.scheduler.start()
        except KeyboardInterrupt:
            logger.info("Scheduler stopped by user")
        except Exception as e:
            logger.error(f"Scheduler error: {e}")
        finally:
            self.scheduler.shutdown()

def main():
    """Main function to run the scheduler."""
    scheduler = ScrapingScheduler()
    scheduler.start()

if __name__ == "__main__":
    main()
